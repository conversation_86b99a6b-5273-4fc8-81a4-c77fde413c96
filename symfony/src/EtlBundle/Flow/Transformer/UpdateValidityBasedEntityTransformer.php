<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use LoginAutonom\EtlBundle\Entity\EtlMapping;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class UpdateValidityBasedEntityTransformer implements
    FlowOneRowTransformerInterface,
    EtlCacheAwareInterface,
    LoggerAwareInterface
{
    use EtlCacheAwareTrait;
    use EtlCommonFunctionsTrait;
    use FlowTransformerTrait;
    use LoggerAwareTrait;

    public function __construct(
        private readonly array $fieldMap,
        private readonly string $targetField,
        private readonly string $cacheKey,
        private readonly string $fromField,
        private readonly string $mappingField,
        private readonly string $entityClass,
        private readonly EntityChangePlanBuilder $changePlanBuilder,
        private readonly EtlEntityModifyHandlerInterface $modifyHandler,
        private readonly ?string $toField = null,
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        if ($row->has($this->targetField)) {
            $entity = $this->updateEntity($row, $context);
            return $row->set($context->entryFactory()->create($this->targetField, $entity));
        } else {
            return $row;
        }
    }

    private function updateEntity(Row $row, FlowContext $context): object
    {
        $entityCache = $this->getEntityCache($context);
        $this->logger->debug('Get original entity for update');
        $originalEntity = $this->getEntityByMapping($row, $context);

        $this->logger->debug('Create change plan for update');
        $changePlan = $this->changePlanBuilder->reset()
            ->setEntity($originalEntity)
            ->setFieldMap($this->fieldMap)
            ->setFields($this->getValuesFromRow($row))
            ->build();

        if (!$changePlan->hasChanges()) {
            $this->logger->debug('No changes detected, returning original entity');
            return $originalEntity;
        }

        if (isset($this->toField)) {
            $this->logger->debug('Creating ValidityInterval for update');
            $validity = new ValidityInterval(
                $row->get($this->fromField)->value(),
                $row->get($this->toField)->value()
            );
        } else {
            $this->logger->debug('Creating SingleValidity for update');
            $validity = new SingleValidity(
                $row->get($this->fromField)->value()
            );
        }

        $this->logger->debug('Execute update via modifyHandler');
        $entityChanges = $this->modifyHandler->modify(
            new EtlEntityModifyInfo(
                $originalEntity,
                $changePlan,
                $entityCache,
                $validity
            )
        );

        $this->logger->debug('Add update changes to storage');
        $changesStorage = $this->getChangesStorage($row);
        $changesStorage->mergeFrom($entityChanges->getChanges());
        $finalEntity = $entityChanges->getFinalEntity();
        $changesStorage->addChanged($finalEntity);
        $entityCache->add($finalEntity);

        return $finalEntity;
    }

    private function getEntityByMapping(Row $row, FlowContext $context): object
    {
        $entityCache = $this->getEntityCache($context);
        /** @var EtlMapping $mapping */
        $mapping = $row->get($this->mappingField)->value();
        $validityIntervalBasedEntity = $entityCache->get($mapping->getTargetIdentifier(), $this->entityClass);

        if (isset($this->toField) && $row->has($this->toField)) {
            $entitiesByValidity = $validityIntervalBasedEntity->getAllByDateInterval(
                $row->get($this->fromField)->value(),
                $row->get($this->toField)->value(),
            );
            return reset($entitiesByValidity);
        } else {
            return $validityIntervalBasedEntity->getByDay(
                $row->get($this->fromField)->value(),
            );
        }
    }

    private function getValuesFromRow(Row $row): array
    {
        $values = [];
        foreach ($this->fieldMap as $rowField => $entityField) {
            if ($row->has($rowField)) {
                $values[$rowField] = $row->get($rowField)->value();
            }
        }
        return $values;
    }

    private function collectIdentifiers(Row $row, FlowContext $context): array
    {
        $etlCache = $this->getEntityCache($context);
        if (isset($this->mappingField, $this->mappingType) && $etlCache->hasMappingType($this->mappingType)) {
            return $this->collectIdentifiersByMapping($row, $context);
        }

        return $this->collectIdentifiersFromRow($row);
    }

    private function collectIdentifiersByMapping(Row $row, FlowContext $context): array
    {
        if ($row->has($this->mappingField)) {
            /** @var EtlMapping $mapping */
            $mapping = $row->get($this->mappingField)->value();
            return $mapping->getTargetIdentifier();
        }
        $etlCache = $this->getEntityCache($context);
        $sourceIdentifiers = $etlCache->collectIdentifiers(
            $row,
            $etlCache->getSourceMapping($this->mappingType)
        );
        try {
            $mapping = $etlCache->findMapping($sourceIdentifiers);

            return $mapping->getTargetIdentifier();
        } catch (NotFoundException) {
        }

        return [];
    }

    private function collectIdentifiersFromRow(Row $row): array
    {
        $identifiers = [];
        foreach ($this->identifierFields as $identifierFieldName => $fieldName) {
            if (!$row->has($fieldName)) {
                throw new \Exception("Identifier field not found: {$identifierFieldName} -> {$fieldName}");
            }
            $identifiers[$identifierFieldName] = $row->get($fieldName)->value();
        }

        return $identifiers;
    }
}
