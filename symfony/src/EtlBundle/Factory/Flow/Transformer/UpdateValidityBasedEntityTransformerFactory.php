<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Transformer;

use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\Flow\Transformer\UpdateValidityBasedEntityTransformer;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerFactoryInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

final readonly class UpdateValidityBasedEntityTransformerFactory implements FlowTransformerFactoryInterface
{
    public function __construct(
        private EntityChangePlanBuilder $entityChangePlanBuilder,
        private MultiEntitiesByIdentifiersProvider $entitiesByIdentifiersProvider,
        #[TaggedLocator(EtlEntityModifyHandlerInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $modifyHandlers,
    ) {
    }

    public function build(array $config): object
    {
        $fieldMap = $config['fieldMap'];
        $targetField = $config['targetField'];
        $cacheKey = $config['cacheKey'];
        $fromField = $config['fromField'];
        $identifierFields = $config['identifierFields'];
        $entityClass = $config['entityClass'];
        $modifyHandler = $this->modifyHandlers->get($config['modifyHandler']);
        $toField = $config['toField'] ?? null;

        return new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $targetField,
            $cacheKey,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->entityChangePlanBuilder,
            $modifyHandler,
            $this->entitiesByIdentifiersProvider,
            $toField
        );
    }

    public static function getName(): string
    {
        return 'update-validity-based-entity-transformer';
    }
}
